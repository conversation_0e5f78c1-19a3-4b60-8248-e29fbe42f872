<template>
  <div class="content-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>专业内容</h1>
      <div class="page-header-desc">
        <div class="data">
          <h1>53</h1>
          <p>全部课程</p>
        </div>
        <div class="data">
          <h1>53</h1>
          <p>已建设知识图谱</p>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧导航标签 -->
      <div class="nav-container">
        <button
          v-for="tab in tabs"
          :key="tab.title"
          @click="switchTab(tab)"
          :class="{ active: activeTab.title === tab.title }"
        >
          {{ tab.title }}
        </button>
      </div>

      <!-- 右侧内容区 -->
      <div class="content">
        <div v-if="activeTab.title === '专业内容'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载专业内容...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorContent" class="retry-btn">重试</button>
          </div>

          <!-- 专业内容数据 -->
          <div v-else class="major-content">
            <!-- 专业介绍 -->
            <div v-if="majorContentData.introduce" class="content-section">
              <div v-html="majorContentData.introduce" class="html-content"></div>
            </div>

            <!-- 专业信息 -->
            <div v-if="majorContentData.info" class="content-section">
              <h3>专业信息</h3>
              <div v-html="majorContentData.info" class="html-content"></div>
            </div>

            <!-- 其他字段（如果有数据的话） -->
            <div v-if="majorContentData.target" class="content-section">
              <h3>培养目标</h3>
              <div v-html="majorContentData.target" class="html-content"></div>
            </div>

            <div v-if="majorContentData.coursePlan" class="content-section">
              <h3>课程计划</h3>
              <div v-html="majorContentData.coursePlan" class="html-content"></div>
            </div>

            <div v-if="majorContentData.course" class="content-section">
              <h3>课程设置</h3>
              <div v-html="majorContentData.course" class="html-content"></div>
            </div>

            <div v-if="majorContentData.relation" class="content-section">
              <h3>相关信息</h3>
              <div v-html="majorContentData.relation" class="html-content"></div>
            </div>

            <div v-if="majorContentData.requirement" class="content-section">
              <h3>专业要求</h3>
              <div v-html="majorContentData.requirement" class="html-content"></div>
            </div>

            <div v-if="majorContentData.teachingPlan" class="content-section">
              <h3>教学计划</h3>
              <div v-html="majorContentData.teachingPlan" class="html-content"></div>
            </div>

            <div v-if="majorContentData.cultivationPlan" class="content-section">
              <h3>培养方案</h3>
              <div v-html="majorContentData.cultivationPlan" class="html-content"></div>
            </div>

            <!-- 如果没有任何内容 -->
            <div v-if="!hasAnyContent" class="empty-content">
              <p>暂无专业内容数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === '专业调研'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载专业调研...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorInvestigation" class="retry-btn">重试</button>
          </div>

          <!-- 专业调研数据 -->
          <div v-else class="major-investigation">
            <div v-if="majorInvestigationData.content" class="content-section">
              <h3>调研内容</h3>
              <div class="investigation-content">
                <p>{{ majorInvestigationData.content }}</p>
              </div>
            </div>

            <!-- 调研信息 -->
            <div v-if="majorInvestigationData.createUserName || majorInvestigationData.updateUserName" class="meta-info">
              <h3>调研信息</h3>
              <div class="meta-details">
                <p v-if="majorInvestigationData.createUserName">
                  <strong>创建者：</strong>{{ majorInvestigationData.createUserName }}
                </p>
                <p v-if="majorInvestigationData.createTime">
                  <strong>创建时间：</strong>{{ formatTime(majorInvestigationData.createTime) }}
                </p>
                <p v-if="majorInvestigationData.updateUserName">
                  <strong>更新者：</strong>{{ majorInvestigationData.updateUserName }}
                </p>
                <p v-if="majorInvestigationData.updateTime">
                  <strong>更新时间：</strong>{{ formatTime(majorInvestigationData.updateTime) }}
                </p>
              </div>
            </div>

            <!-- 如果没有内容 -->
            <div v-if="!majorInvestigationData.content" class="empty-content">
              <p>暂无专业调研数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === '人才培养方案'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载人才培养方案...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorCultivationList" class="retry-btn">重试</button>
          </div>

          <!-- 人才培养方案列表数据 -->
          <div v-else class="major-cultivation">
            <div v-if="majorCultivationData.records && majorCultivationData.records.length > 0" class="cultivation-list">
              <h3>人才培养方案列表</h3>
              <div class="list-container">
                <div
                  v-for="item in majorCultivationData.records"
                  :key="item.cultivationId"
                  class="cultivation-item"
                >
                  <div class="item-header">
                    <h4 class="item-title">{{ item.name || '未命名方案' }}</h4>
                    <span class="item-year" v-if="item.year">{{ item.year }}年</span>
                  </div>

                  <div class="item-content">
                    <div class="file-info">
                      <p><strong>文件名：</strong>{{ item.fileName || '未知' }}</p>
                      <p><strong>文件类型：</strong>{{ item.fileType || '未知' }}</p>
                      <p><strong>文件大小：</strong>{{ formatFileSize(item.fileSize) }}</p>
                    </div>

                    <div class="meta-info">
                      <p v-if="item.createUserName">
                        <strong>创建者：</strong>{{ item.createUserName }}
                      </p>
                      <p v-if="item.createTime">
                        <strong>创建时间：</strong>{{ formatTime(item.createTime) }}
                      </p>
                      <p v-if="item.updateUserName">
                        <strong>更新者：</strong>{{ item.updateUserName }}
                      </p>
                      <p v-if="item.updateTime">
                        <strong>更新时间：</strong>{{ formatTime(item.updateTime) }}
                      </p>
                    </div>
                  </div>

                  <div class="item-actions" v-if="item.filePath">
                    <a :href="item.filePath" target="_blank" class="download-btn">
                      <i class="fas fa-download"></i>
                      下载文件
                    </a>
                  </div>
                </div>
              </div>

              <!-- 分页信息 -->
              <div v-if="majorCultivationData.total > 0" class="pagination-info">
                <p>
                  共 {{ majorCultivationData.total }} 条记录，
                  第 {{ majorCultivationData.current }} / {{ majorCultivationData.pages }} 页
                </p>
              </div>
            </div>

            <!-- 如果没有数据 -->
            <div v-else class="empty-content">
              <p>暂无人才培养方案数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === 'AI岗位分析'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载AI岗位分析...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorAiJob" class="retry-btn">重试</button>
          </div>

          <!-- AI岗位分析数据 -->
          <div v-else class="major-ai-job">
            <div v-if="majorAiJobData.content" class="content-section">
              <h3>AI岗位分析内容</h3>
              <div class="ai-job-content">
                <p>{{ majorAiJobData.content }}</p>
              </div>
            </div>

            <!-- 分析信息 -->
            <div v-if="majorAiJobData.createUserName || majorAiJobData.updateUserName" class="meta-info">
              <h3>分析信息</h3>
              <div class="meta-details">
                <p v-if="majorAiJobData.createUserName">
                  <strong>创建者：</strong>{{ majorAiJobData.createUserName }}
                </p>
                <p v-if="majorAiJobData.createTime">
                  <strong>创建时间：</strong>{{ formatTime(majorAiJobData.createTime) }}
                </p>
                <p v-if="majorAiJobData.updateUserName">
                  <strong>更新者：</strong>{{ majorAiJobData.updateUserName }}
                </p>
                <p v-if="majorAiJobData.updateTime">
                  <strong>更新时间：</strong>{{ formatTime(majorAiJobData.updateTime) }}
                </p>
              </div>
            </div>

            <!-- 如果没有内容 -->
            <div v-if="!majorAiJobData.content" class="empty-content">
              <p>暂无AI岗位分析数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getMajorContent, getMajorInvestigation, getMajorAiJob, getMajorCultivationList } from '@/api/public/profession/majorContent';
import { ElMessage } from 'element-plus';

const tabs = [
  { title: '专业内容' },
  { title: '专业调研' },
  { title: '人才培养方案' },
  { title: 'AI岗位分析' },
];
const activeTab = ref(tabs[0]);

// 专业内容数据
const majorContentData = ref({
  contentId: '',
  introduce: '',
  info: '',
  target: '',
  coursePlan: '',
  course: '',
  relation: '',
  requirement: '',
  teachingPlan: '',
  cultivationPlan: '',
  createTime: null,
  updateTime: null
});

// 专业调研数据
const majorInvestigationData = ref({
  investigationId: '',
  content: '',
  createUserId: '',
  createTime: null,
  updateUserId: '',
  updateTime: null,
  createUserName: '',
  updateUserName: ''
});

// AI岗位分析数据
const majorAiJobData = ref({
  jobAnalysisId: '',
  content: '',
  createUserId: '',
  createTime: null,
  updateUserId: '',
  updateTime: null,
  createUserName: '',
  updateUserName: ''
});

// 人才培养方案数据
const majorCultivationData = ref({
  records: [],
  total: 0,
  size: 10,
  current: 1,
  pages: 0
});

// 加载状态
const isLoading = ref(false);
const error = ref(null);

// 检查是否有任何内容
const hasAnyContent = computed(() => {
  const data = majorContentData.value;
  return data.introduce || data.info || data.target || data.coursePlan ||
         data.course || data.relation || data.requirement ||
         data.teachingPlan || data.cultivationPlan;
});

// 获取专业内容数据
const fetchMajorContent = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorContent();
    console.log('专业内容API响应:', response);

    if (response && response.result) {
      majorContentData.value = response.result;
    } else {
      throw new Error('响应数据格式错误');
    }
  } catch (err) {
    console.error('获取专业内容失败:', err);
    error.value = err.message || '获取专业内容失败';
    ElMessage.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

// 获取专业调研数据
const fetchMajorInvestigation = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorInvestigation();
    console.log('专业调研API响应:', response);

    if (response && response.result) {
      majorInvestigationData.value = response.result;
    } else {
      throw new Error('响应数据格式错误');
    }
  } catch (err) {
    console.error('获取专业调研失败:', err);
    error.value = err.message || '获取专业调研失败';
    ElMessage.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

// 获取AI岗位分析数据
const fetchMajorAiJob = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorAiJob();
    console.log('AI岗位分析API响应:', response);

    if (response && response.result) {
      majorAiJobData.value = response.result;
    } else {
      throw new Error('响应数据格式错误');
    }
  } catch (err) {
    console.error('获取AI岗位分析失败:', err);
    error.value = err.message || '获取AI岗位分析失败';
    ElMessage.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

// 获取人才培养方案列表数据
const fetchMajorCultivationList = async (params = {}) => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorCultivationList(params);
    console.log('人才培养方案API响应:', response);

    if (response && response.result) {
      majorCultivationData.value = response.result;
    } else {
      throw new Error('响应数据格式错误');
    }
  } catch (err) {
    console.error('获取人才培养方案失败:', err);
    error.value = err.message || '获取人才培养方案失败';
    ElMessage.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

function switchTab(tab) {
  activeTab.value = tab;

  // 根据不同标签获取对应数据
  if (tab.title === '专业内容') {
    fetchMajorContent();
  } else if (tab.title === '专业调研') {
    fetchMajorInvestigation();
  } else if (tab.title === 'AI岗位分析') {
    fetchMajorAiJob();
  } else if (tab.title === '人才培养方案') {
    fetchMajorCultivationList();
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '暂无';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN');
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '未知';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// 组件挂载时，根据当前标签获取对应数据
onMounted(() => {
  if (activeTab.value.title === '专业内容') {
    fetchMajorContent();
  } else if (activeTab.value.title === '专业调研') {
    fetchMajorInvestigation();
  } else if (activeTab.value.title === 'AI岗位分析') {
    fetchMajorAiJob();
  } else if (activeTab.value.title === '人才培养方案') {
    fetchMajorCultivationList();
  }
});
</script>

<style lang="scss" scoped>
/* 页面整体样式 */
.content-container {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }
  
  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between; 
    
    h1 { 
      font-size: 3rem; 
      font-weight: 400;
      margin: 0; 
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); 
    }
    
    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;
      
      .data {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;
        
        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          h1 {
            margin: 0; 
            line-height: 1;
            font-size: 3rem; 
          }
          
          p {
            margin: 0 0 0 0.2em;
            line-height: 1; 
            align-self: flex-end; 
            font-size: 1rem; 
          }
        }
      }
    }
  }
}

/* 主内容区样式 */
.main-content {
  display: flex;
  gap: 1.5vw;
  min-height: 70vh;
  background-color: white;
  border-radius: 1vw 1vw 0 0;
  overflow: hidden;
  padding: 2vw;
  margin-top: 10vw;
  
  .nav-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1vw;
    margin-top: 1.5vw;
    
    h2 {
      font-size: 1.2rem;
      font-weight: 700;
    }
    
    button {
      padding: 0.8vw 2vw;
      border: $course-tabs-solid solid 1px;
      background-color: white;
      cursor: pointer;
      border-radius: 2vw;
      font-size: 1.2rem;
      transition: all 0.3s ease;
      text-align: left;
      
      &:hover {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
      }
      
      &.active {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
        font-weight: bold;
      }
    }
  }
  
  .content {
    flex: 4;
    padding: 1vw;
    border-radius: 0.3vw;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;

      .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #8a6de3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
      }

      p {
        color: #666;
        font-size: 16px;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;

      .error-icon {
        font-size: 36px;
        margin-bottom: 15px;
      }

      .error-message {
        color: #e74c3c;
        font-size: 16px;
        margin-bottom: 20px;
        text-align: center;
      }

      .retry-btn {
        padding: 8px 16px;
        background: #8a6de3;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;

        &:hover {
          background: #7a5dd3;
        }
      }
    }

    .major-content {
      .content-section {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .html-content {
          line-height: 1.6;
          color: #666;

          h1, h2, h3, h4, h5, h6 {
            color: #333;
            margin: 15px 0 10px 0;
          }

          p {
            margin-bottom: 12px;
          }

          ul, ol {
            margin: 10px 0;
            padding-left: 20px;
          }

          li {
            margin-bottom: 5px;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }

    .major-investigation {
      .content-section {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .investigation-content {
          line-height: 1.6;
          color: #666;

          p {
            margin-bottom: 12px;
            font-size: 16px;
          }
        }
      }

      .meta-info {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .meta-details {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #8a6de3;

          p {
            margin-bottom: 8px;
            color: #666;
            font-size: 14px;

            strong {
              color: #333;
              margin-right: 8px;
            }
          }

          p:last-child {
            margin-bottom: 0;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }

    .major-cultivation {
      .cultivation-list {
        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 20px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .list-container {
          .cultivation-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 15px;

              .item-title {
                color: #333;
                font-size: 16px;
                font-weight: 600;
                margin: 0;
              }

              .item-year {
                background: #8a6de3;
                color: white;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
              }
            }

            .item-content {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin-bottom: 15px;

              .file-info, .meta-info {
                p {
                  margin-bottom: 8px;
                  color: #666;
                  font-size: 14px;

                  strong {
                    color: #333;
                    margin-right: 8px;
                  }
                }

                p:last-child {
                  margin-bottom: 0;
                }
              }
            }

            .item-actions {
              text-align: right;

              .download-btn {
                display: inline-block;
                background: #8a6de3;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                text-decoration: none;
                font-size: 14px;
                transition: background-color 0.3s;

                &:hover {
                  background: #7a5dd3;
                }

                i {
                  margin-right: 6px;
                }
              }
            }
          }
        }

        .pagination-info {
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-top: 20px;

          p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>