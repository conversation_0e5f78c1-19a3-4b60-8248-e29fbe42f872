<template>
  <div class="graduation-projects-page">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载项目详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchProjectDetail" class="retry-btn">重试</button>
    </div>

    <!-- 项目内容 -->
    <div v-else>
      <!-- 第一个父级板块：项目概览 -->
      <section class="project-overview-section">
        <div class="overview-container">
          <!-- 左侧：项目封面 -->
          <div class="project-cover-area">
            <div class="cover-wrapper">
              <img
                :src="projectData.coverImage || '/src/assets/courseCover/cover1.png'"
                :alt="projectData.name"
                class="cover-image"
              />
              <div class="cover-overlay">
                <h3 class="cover-title">{{ projectData.name || '项目名称' }}</h3>
              </div>
            </div>
          </div>

          <!-- 右侧：项目信息 -->
          <div class="project-info-area">
            <div class="info-header">
              <div class="header-item">
                <h3 class="header-label">毕设名</h3>
                <p class="header-value">{{ projectData.name || '暂无' }}</p>
              </div>
              <div class="header-item">
                <h3 class="header-label">作者</h3>
                <p class="header-value">{{ projectData.author || '暂无' }}</p>
              </div>
              <div class="header-item">
                <h3 class="header-label">毕业年份</h3>
                <p class="header-value">{{ projectData.graduationYear || '暂无' }}</p>
              </div>
              <div class="header-item">
                <h3 class="header-label">简介</h3>
                <p class="header-value">{{ projectData.brief || '暂无简介' }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第二个父级板块：详细内容 -->
      <section class="project-content-section">
        <div class="content-container">
          <!-- 左侧：导航按钮 -->
          <div class="navigation-area">
            <div class="nav-buttons">
              <button
                v-for="(item, index) in navItems"
                :key="item.key"
                :class="['nav-btn', { active: activeIndex === index }]"
                @click="handleNavClick(index, item.key)"
              >
                {{ item.label }}
              </button>
            </div>
          </div>

          <!-- 右侧：内容区域 -->
          <div class="content-area">
            <div class="content-wrapper">
              <!-- 项目介绍 -->
              <div v-if="activeContentType === 'introduction'" class="project-detail">
                <h2>项目介绍</h2>
                <div class="detail-content">
                  <p><strong>项目简介：</strong>{{ projectData.brief || '暂无简介' }}</p>
                  <p><strong>详细介绍：</strong>{{ projectData.introduction || '暂无详细介绍' }}</p>
                  <p><strong>创建时间：</strong>{{ formatTime(projectData.createTime) }}</p>
                  <p><strong>更新时间：</strong>{{ formatTime(projectData.updateTime) }}</p>
                </div>
              </div>

              <!-- 毕设论文 -->
              <div v-else-if="activeContentType === 'thesis'" class="thesis-content">
                <h2>毕设论文</h2>
                <div class="thesis-detail">
                  <div v-if="projectData.thesisUrl" class="thesis-link">
                    <a :href="projectData.thesisUrl" target="_blank" class="download-link">
                      <i class="fas fa-download"></i>
                      下载论文
                    </a>
                  </div>
                  <div class="thesis-text">
                    <h3>论文内容</h3>
                    <p>{{ projectData.thesisContent || '暂无论文内容' }}</p>
                  </div>
                </div>
              </div>

              <!-- 项目图片 -->
              <div v-else-if="activeContentType === 'gallery'" class="image-gallery">
                <h2>项目图片</h2>
                <div v-if="imageList.length === 0" class="empty-state">
                  <p>暂无相关图片</p>
                </div>
                <div v-else class="image-grid">
                  <div
                    v-for="image in imageList"
                    :key="image.imageId"
                    class="image-card"
                  >
                    <img :src="image.imageUrl" :alt="image.description" class="gallery-image" />
                    <div class="image-info">
                      <p>{{ image.description || '无描述' }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 项目视频 -->
              <div v-else-if="activeContentType === 'videos'" class="video-gallery">
                <h2>项目视频</h2>
                <div v-if="videoList.length === 0" class="empty-state">
                  <p>暂无相关视频</p>
                </div>
                <div v-else class="video-grid">
                  <div
                    v-for="video in videoList"
                    :key="video.videoId"
                    class="video-card"
                  >
                    <video :src="video.videoUrl" controls class="gallery-video">
                      您的浏览器不支持视频播放
                    </video>
                    <div class="video-info">
                      <p>{{ video.description || '无描述' }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 其他内容组件 -->
              <component
                v-else
                :is="currentComponent"
                class="content-component"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getGraduationWorkDetail,
  getGraduationImageList,
  getGraduationVideoList
} from '@/api/public/resource/graduation'
import ProjectIntroduction from '../components/graduation-content/ProjectIntroduction.vue'
import ThesisPaper from '../components/graduation-content/ThesisPaper.vue'
import ProjectGallery from '../components/graduation-content/ProjectGallery.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const activeContentType = ref('introduction')
const activeIndex = ref(0)
const isLoading = ref(false)
const error = ref(null)

// 导航项目
const navItems = [
  { key: 'introduction', label: '项目介绍' },
  { key: 'thesis', label: '毕设论文' },
  { key: 'gallery', label: '项目图片' },
  { key: 'videos', label: '项目视频' }
]

// 项目数据
const projectData = ref({
  workId: '',
  name: '',
  coverImage: '',
  author: '',
  graduationYear: '',
  brief: '',
  introduction: '',
  thesisUrl: '',
  thesisContent: '',
  createTime: null,
  updateTime: null
})

// 图片数据
const imageList = ref([])

// 视频数据
const videoList = ref([])

// 获取项目详情数据
const fetchProjectDetail = async () => {
  try {
    isLoading.value = true
    error.value = null

    const workId = route.params.workId
    if (!workId) {
      throw new Error('缺少作品ID参数')
    }

    // 获取作品详情
    const detailResponse = await getGraduationWorkDetail(workId)
    if (detailResponse && detailResponse.result) {
      projectData.value = detailResponse.result
    }

    // 获取图片列表
    try {
      const imageResponse = await getGraduationImageList(workId)
      if (imageResponse && imageResponse.result) {
        imageList.value = imageResponse.result
      }
    } catch (imageError) {
      console.warn('获取图片列表失败:', imageError)
    }

    // 获取视频列表
    try {
      const videoResponse = await getGraduationVideoList(workId)
      if (videoResponse && videoResponse.result) {
        videoList.value = videoResponse.result
      }
    } catch (videoError) {
      console.warn('获取视频列表失败:', videoError)
    }

  } catch (err) {
    console.error('获取项目详情失败:', err)
    error.value = err.message || '获取项目详情失败'
    ElMessage.error(error.value)
  } finally {
    isLoading.value = false
  }
}

// 内容组件映射
const componentMap = {
  'introduction': ProjectIntroduction,
  'thesis': ThesisPaper,
  'gallery': ProjectGallery
}

// 当前显示的组件
const currentComponent = computed(() => componentMap[activeContentType.value])

// 处理导航点击
const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  activeContentType.value = tabKey
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '暂无'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  console.log('GraduationProjects页面已挂载');
  console.log('路由参数:', route.params);
  console.log('workId:', route.params.workId);
  fetchProjectDetail()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.graduation-projects-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    min-height: 400px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #8a6de3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    min-height: 400px;

    .error-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .error-message {
      color: #e74c3c;
      font-size: 16px;
      margin-bottom: 20px;
      text-align: center;
    }

    .retry-btn {
      padding: 10px 20px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;

      &:hover {
        background: #7a5dd3;
      }
    }
  }

  // 第一个父级板块：项目概览
  .project-overview-section {
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($primary-color, 0.02) 100%);

    .overview-container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 400px 1fr;
      gap: 40px;
      align-items: center;
    }

    .project-cover-area {
      .cover-wrapper {
        position: relative;
        width: 100%;
        height: 280px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

        .cover-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .cover-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
          padding: 24px 20px 20px;

          .cover-title {
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          }
        }

        &:hover .cover-image {
          transform: scale(1.05);
        }
      }
    }

    .project-info-area {
      .info-header {
        .header-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .header-label {
            font-size: 16px;
            font-weight: 600;
            color: $primary-color;
            margin: 0 0 8px 0;
            position: relative;
            padding-left: 16px;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 16px;
              background: $primary-color;
              border-radius: 2px;
            }
          }

          .header-value {
            font-size: 18px;
            font-weight: 500;
            color: $text-color;
            margin: 0;
            line-height: 1.6;
            padding-left: 16px;
          }
        }
      }
    }
  }

  // 第二个父级板块：详细内容
  .project-content-section {
    padding: 40px 20px;

    .content-container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 240px 1fr;
      gap: 40px;
      align-items: flex-start;
    }

    .navigation-area {
      position: sticky;
      top: 20px;

      .nav-buttons {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;

        .nav-btn {
          width: 100%;
          padding: 16px 20px;
          background: white;
          border: none;
          border-bottom: 1px solid $border-color;
          font-size: 16px;
          font-weight: 500;
          color: $text-color;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: left;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background: rgba($primary-color, 0.05);
            color: $primary-color;
          }

          &.active {
            background: $primary-color;
            color: white;
            font-weight: 600;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              background: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }

    .content-area {
      .content-wrapper {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        min-height: 600px;
        padding: 30px;

        .content-component {
          width: 100%;
        }

        .project-detail, .thesis-content, .image-gallery, .video-gallery {
          h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: 600;
          }

          .empty-state {
            text-align: center;
            padding: 40px;
            color: #999;
          }
        }

        .project-detail {
          .detail-content {
            p {
              margin-bottom: 12px;
              line-height: 1.6;
              color: #666;

              strong {
                color: #333;
                margin-right: 8px;
              }
            }
          }
        }

        .thesis-content {
          .thesis-detail {
            .thesis-link {
              margin-bottom: 20px;

              .download-link {
                display: inline-block;
                background: #8a6de3;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                text-decoration: none;
                font-size: 16px;
                transition: background-color 0.3s;

                &:hover {
                  background: #7a5dd3;
                }

                i {
                  margin-right: 8px;
                }
              }
            }

            .thesis-text {
              h3 {
                color: #333;
                margin-bottom: 15px;
                font-size: 18px;
              }

              p {
                color: #666;
                line-height: 1.6;
              }
            }
          }
        }

        .image-gallery {
          .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;

            .image-card {
              background: #f8f9fa;
              border-radius: 12px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transition: transform 0.3s ease;

              &:hover {
                transform: translateY(-4px);
              }

              .gallery-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
              }

              .image-info {
                padding: 15px;

                p {
                  margin: 0;
                  color: #666;
                  font-size: 14px;
                  line-height: 1.4;
                }
              }
            }
          }
        }

        .video-gallery {
          .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 30px;

            .video-card {
              background: #f8f9fa;
              border-radius: 12px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

              .gallery-video {
                width: 100%;
                height: 250px;
                object-fit: cover;
              }

              .video-info {
                padding: 15px;

                p {
                  margin: 0;
                  color: #666;
                  font-size: 14px;
                  line-height: 1.4;
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 1024px) {
  .graduation-projects-page {
    .project-overview-section {
      .overview-container {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
      }

      .project-cover-area .cover-wrapper {
        max-width: 400px;
        margin: 0 auto;
      }
    }

    .project-content-section {
      .content-container {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .navigation-area {
        position: static;

        .nav-buttons {
          display: flex;

          .nav-btn {
            flex: 1;
            border-bottom: none;
            border-right: 1px solid $border-color;

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .graduation-projects-page {
    .project-overview-section {
      padding: 24px 16px;

      .overview-container {
        gap: 24px;
      }

      .project-cover-area .cover-wrapper {
        height: 200px;

        .cover-overlay .cover-title {
          font-size: 18px;
        }
      }

      .project-info-area .info-header .header-item {
        margin-bottom: 20px;

        .header-label {
          font-size: 14px;
        }

        .header-value {
          font-size: 16px;
        }
      }
    }

    .project-content-section {
      padding: 24px 16px;

      .navigation-area .nav-buttons .nav-btn {
        padding: 14px 16px;
        font-size: 14px;
      }

      .content-area .content-wrapper {
        padding: 20px;

        .image-gallery .image-grid {
          grid-template-columns: 1fr;
        }

        .video-gallery .video-grid {
          grid-template-columns: 1fr;

          .video-card .gallery-video {
            height: 200px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .graduation-projects-page {
    .project-overview-section {
      padding: 20px 12px;

      .project-cover-area .cover-wrapper {
        height: 180px;
      }
    }

    .project-content-section {
      padding: 20px 12px;

      .navigation-area .nav-buttons {
        flex-direction: column;

        .nav-btn {
          border-right: none;
          border-bottom: 1px solid $border-color;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
}
</style>