<template>
  <div class="page-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载项目详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchProjectDetail" class="retry-btn">重试</button>
    </div>

    <!-- 项目内容 -->
    <div v-else>
      <!-- 项目简介板块 -->
      <section class="project-intro-section">
        <div class="intro-grid">
          <!-- 左侧封面图片区域 -->
          <div class="video-container">
            <div class="video-wrapper">
              <img
                :src="projectData.coverImage || '/src/assets/img/default-cover.jpg'"
                :alt="projectData.name"
                class="project-cover"
              />
              <!-- 如果有视频，显示视频播放器 -->
              <div v-if="videoList.length > 0" class="video-overlay">
                <video
                  v-if="videoList[0].introVideo"
                  :src="videoList[0].introVideo"
                  controls
                  class="project-video"
                >
                  您的浏览器不支持视频播放
                </video>
              </div>
            </div>
            <div class="video-info">
              <h1 class="project-title">{{ projectData.name || '项目名称' }}</h1>
              <p class="project-subtitle">{{ projectData.description || '项目描述' }}</p>
              <div class="project-meta">
                <span v-if="projectData.semester" class="meta-item">
                  <i class="fas fa-calendar"></i>
                  {{ projectData.semester }}
                </span>
                <span v-if="projectData.subject" class="meta-item">
                  <i class="fas fa-book"></i>
                  {{ projectData.subject }}
                </span>
                <span v-if="projectData.leader" class="meta-item">
                  <i class="fas fa-user"></i>
                  {{ projectData.leader }}
                </span>
                <span v-if="projectData.school" class="meta-item">
                  <i class="fas fa-university"></i>
                  {{ projectData.school }}
                </span>
              </div>
            </div>
          </div>

          <!-- 右侧统计数据区域 -->
          <div class="stats-container">
            <div class="stats-grid">
              <StatCard
                v-for="stat in stats"
                :key="stat.key"
                :title="stat.title"
                :value="stat.value"
                :icon="stat.icon"
              />
            </div>
            <div class="action-buttons">
              <button class="primary-btn" @click="startExperiment">
                <i class="fas fa-play"></i>
                开始实验
              </button>
              <button class="secondary-btn" @click="scrollToContent">
                <i class="fas fa-info-circle"></i>
                了解更多
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 项目内容板块 -->
      <section class="project-content-section" ref="contentSection">
        <div class="content-layout">
          <!-- 左侧导航 -->
          <div class="navigation-container">
            <ProjectNav @nav-click="handleNavClick" />
          </div>

          <!-- 右侧内容区域 -->
          <div class="content-container">
            <div class="content-wrapper">
              <!-- 项目详细信息 -->
              <div v-if="activeContentType === 'intro'" class="project-detail">
                <h2>项目简介</h2>
                <div class="detail-content">
                  <p><strong>项目内容：</strong>{{ projectData.simulationContent || '暂无详细内容' }}</p>
                  <p><strong>课时：</strong>{{ projectData.classHours || 0 }} 课时</p>
                  <p><strong>创建时间：</strong>{{ formatTime(projectData.createTime) }}</p>
                  <p><strong>更新时间：</strong>{{ formatTime(projectData.updateTime) }}</p>
                </div>
              </div>

              <!-- 文档列表 -->
              <div v-else-if="activeContentType === 'docs'" class="document-list">
                <h2>项目文档</h2>
                <div v-if="documentList.length === 0" class="empty-state">
                  <p>暂无相关文档</p>
                </div>
                <div v-else class="doc-grid">
                  <div
                    v-for="doc in documentList"
                    :key="doc.documentId"
                    class="doc-card"
                  >
                    <h3>{{ doc.documentTitle }}</h3>
                    <p>{{ doc.documentContent }}</p>
                    <a
                      v-if="doc.documentUrl"
                      :href="doc.documentUrl"
                      target="_blank"
                      class="doc-link"
                    >
                      查看文档
                    </a>
                  </div>
                </div>
              </div>

              <!-- 视频列表 -->
              <div v-else-if="activeContentType === 'experiment'" class="video-list">
                <h2>实验视频</h2>
                <div v-if="videoList.length === 0" class="empty-state">
                  <p>暂无相关视频</p>
                </div>
                <div v-else class="video-grid">
                  <div
                    v-for="video in videoList"
                    :key="video.videoId"
                    class="video-card"
                  >
                    <div v-if="video.introVideo" class="video-item">
                      <h4>介绍视频</h4>
                      <video :src="video.introVideo" controls class="video-player">
                        您的浏览器不支持视频播放
                      </video>
                    </div>
                    <div v-if="video.guideVideo" class="video-item">
                      <h4>指导视频</h4>
                      <video :src="video.guideVideo" controls class="video-player">
                        您的浏览器不支持视频播放
                      </video>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 其他内容组件 -->
              <component
                v-else
                :is="currentComponent"
                class="content-component"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getSimulationDetail,
  getSimulationDocumentList,
  getSimulationVideoList
} from '@/api/public/resource/simulation'
import StatCard from '../components/StatCard.vue'
import ProjectNav from '../components/ProjectNav.vue'
import ProjectIntro from '../components/project-content/ProjectIntro.vue'
import ExperimentContent from '../components/project-content/ExperimentContent.vue'
import ProjectDocs from '../components/project-content/ProjectDocs.vue'
import ProjectDownload from '../components/project-content/ProjectDownload.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const contentSection = ref(null)
const activeContentType = ref('intro')
const isLoading = ref(false)
const error = ref(null)

// 项目数据
const projectData = ref({
  simulationId: '',
  name: '',
  coverImage: '',
  description: '',
  semester: '',
  visitCount: 0,
  studentCount: 0,
  practiceCount: 0,
  experimentResultCount: 0,
  classHours: 0,
  leader: '',
  school: '',
  subject: '',
  simulationContent: '',
  createTime: null,
  updateTime: null
})

// 文档数据
const documentList = ref([])

// 视频数据
const videoList = ref([])

// 统计数据计算属性
const stats = computed(() => [
  {
    key: 'visits',
    title: '累计访问量',
    value: projectData.value.visitCount || 0,
    icon: 'fas fa-eye'
  },
  {
    key: 'students',
    title: '累计学生数',
    value: projectData.value.studentCount || 0,
    icon: 'fas fa-user-graduate'
  },
  {
    key: 'exercises',
    title: '累计练习数',
    value: projectData.value.practiceCount || 0,
    icon: 'fas fa-dumbbell'
  },
  {
    key: 'results',
    title: '累计实验结果',
    value: projectData.value.experimentResultCount || 0,
    icon: 'fas fa-chart-bar'
  }
])

// 获取项目详情数据
const fetchProjectDetail = async () => {
  try {
    isLoading.value = true
    error.value = null

    const simulationId = route.params.simulationId
    if (!simulationId) {
      throw new Error('缺少项目ID参数')
    }

    // 获取项目详情
    const detailResponse = await getSimulationDetail(simulationId)
    if (detailResponse && detailResponse.result) {
      projectData.value = detailResponse.result
    }

    // 获取文档列表
    try {
      const docResponse = await getSimulationDocumentList(simulationId)
      if (docResponse && docResponse.result) {
        documentList.value = docResponse.result
      }
    } catch (docError) {
      console.warn('获取文档列表失败:', docError)
    }

    // 获取视频列表
    try {
      const videoResponse = await getSimulationVideoList(simulationId)
      if (videoResponse && videoResponse.result) {
        videoList.value = videoResponse.result
      }
    } catch (videoError) {
      console.warn('获取视频列表失败:', videoError)
    }

  } catch (err) {
    console.error('获取项目详情失败:', err)
    error.value = err.message || '获取项目详情失败'
    ElMessage.error(error.value)
  } finally {
    isLoading.value = false
  }
}

// 内容组件映射
const componentMap = {
  'intro': ProjectIntro,
  'experiment': ExperimentContent,
  'docs': ProjectDocs,
  'download': ProjectDownload
}

// 当前显示的组件
const currentComponent = computed(() => componentMap[activeContentType.value])

// 处理导航点击
const handleNavClick = (tabKey) => {
  activeContentType.value = tabKey
}

// 开始实验
const startExperiment = () => {
  console.log('开始实验')
  // 这里可以跳转到实验页面或打开实验模块
}

// 滚动到内容区域
const scrollToContent = () => {
  if (contentSection.value) {
    contentSection.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '暂无'
  const date = new Date(timestamp * 1000) // 假设是秒级时间戳
  return date.toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProjectDetail()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.page-container {
  font-family: 'Segoe UI', sans-serif;
  min-height: 100vh;
  padding: 8vw 1.563vw;
  background-image: url('@/assets/img/General/background-gradient.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: top;
  background-attachment: fixed;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 400px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #8a6de3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 400px;

  .error-icon {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .error-message {
    color: #e74c3c;
    font-size: 16px;
    margin-bottom: 20px;
    text-align: center;
  }

  .retry-btn {
    padding: 10px 20px;
    background: #8a6de3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;

    &:hover {
      background: #7a5dd3;
    }
  }
}

.project-intro-section {
  width: 90vw;
  background: white;
  padding: 1.042vw;
  border-radius: 0.313vw;
  box-shadow: 0 0.052vw 0.208vw rgba(0, 0, 0, 0.1);
  margin: 1.042vw auto;

  .intro-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
  }

  .video-container {
    .video-wrapper {
      position: relative;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      background: #000;

      .project-cover {
        width: 100%;
        height: auto;
        min-height: 300px;
        object-fit: cover;
      }

      .project-video {
        width: 100%;
        height: auto;
        min-height: 300px;
        object-fit: cover;
      }

      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 1;
        }
      }
    }

    .video-info {
      margin-top: 24px;

      .project-title {
        font-size: 32px;
        font-weight: 700;
        color: $text-color;
        margin: 0 0 12px 0;
        line-height: 1.2;
      }

      .project-subtitle {
        font-size: 18px;
        color: rgba($text-color, 0.7);
        margin: 0 0 16px 0;
        line-height: 1.5;
      }

      .project-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 6px;
          background: rgba(138, 109, 227, 0.1);
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 14px;
          color: #8a6de3;

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  .stats-container {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-bottom: 32px;
    }

    .action-buttons {
      display: flex;
      gap: 16px;

      .primary-btn,
      .secondary-btn {
        flex: 1;
        padding: 16px 24px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        i {
          font-size: 18px;
        }
      }

      .primary-btn {
        background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba($primary-color, 0.3);
        }
      }

      .secondary-btn {
        background: white;
        color: $primary-color;
        border: 2px solid $primary-color;

        &:hover {
          background: $primary-color;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba($primary-color, 0.2);
        }
      }
    }
  }
}

.project-content-section {
  width: 90vw;
  background: white;
  padding: 1.042vw;
  border-radius: 0.313vw;
  box-shadow: 0 0.052vw 0.208vw rgba(0, 0, 0, 0.1);
  margin: 1.042vw auto;

  .content-layout {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
    align-items: flex-start;
  }

  .navigation-container {
    position: sticky;
    top: 20px;
  }

  .content-container {
    min-height: 600px;

    .content-wrapper {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      padding: 30px;

      .content-component {
        min-height: 500px;
      }

      .project-detail {
        h2 {
          color: #333;
          margin-bottom: 20px;
          font-size: 24px;
          font-weight: 600;
        }

        .detail-content {
          p {
            margin-bottom: 12px;
            line-height: 1.6;
            color: #666;

            strong {
              color: #333;
              margin-right: 8px;
            }
          }
        }
      }

      .document-list, .video-list {
        h2 {
          color: #333;
          margin-bottom: 20px;
          font-size: 24px;
          font-weight: 600;
        }

        .empty-state {
          text-align: center;
          padding: 40px;
          color: #999;
        }

        .doc-grid {
          display: grid;
          gap: 20px;

          .doc-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;

            h3 {
              color: #333;
              margin-bottom: 10px;
              font-size: 18px;
            }

            p {
              color: #666;
              margin-bottom: 15px;
              line-height: 1.5;
            }

            .doc-link {
              display: inline-block;
              background: #8a6de3;
              color: white;
              padding: 8px 16px;
              border-radius: 6px;
              text-decoration: none;
              font-size: 14px;
              transition: background-color 0.3s;

              &:hover {
                background: #7a5dd3;
              }
            }
          }
        }

        .video-grid {
          display: grid;
          gap: 30px;

          .video-card {
            .video-item {
              margin-bottom: 20px;

              h4 {
                color: #333;
                margin-bottom: 10px;
                font-size: 16px;
              }

              .video-player {
                width: 100%;
                max-width: 600px;
                height: auto;
                border-radius: 8px;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .project-intro-section {
    .intro-grid {
      grid-template-columns: 1fr;
      gap: 32px;
      text-align: center;
    }

    .stats-container .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .project-content-section {
    .content-layout {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .navigation-container {
      position: static;
    }
  }
}

@media (max-width: 768px) {
  .project-intro-section {
    padding: 24px 16px;

    .intro-grid {
      gap: 24px;
    }

    .video-container .video-info {
      .project-title {
        font-size: 24px;
      }

      .project-subtitle {
        font-size: 16px;
      }
    }

    .stats-container {
      .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .action-buttons {
        flex-direction: column;

        .primary-btn,
        .secondary-btn {
          padding: 14px 20px;
          font-size: 14px;
        }
      }
    }
  }

  .project-content-section {
    padding: 24px 16px;

    .content-layout {
      gap: 20px;
    }
  }
}

@media (max-width: 480px) {
  .project-intro-section {
    .video-container .video-info .project-title {
      font-size: 20px;
    }

    .stats-container .action-buttons {
      gap: 12px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>