<template>
  <div class="heritage-detail-page">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载非遗详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchHeritageDetail" class="retry-btn">重试</button>
    </div>

    <!-- 正常内容 -->
    <template v-else>
      <ImageCarousel :heritageData="heritageData" />
      <InfoSection :heritageData="heritageData" />
    </template>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getHeritageDetail, getHeritageImageList } from '@/api/public/resource/heritage'
import ImageCarousel from '../components/heritage-content/ImageCarousel.vue'
import InfoSection from '../components/heritage-content/InfoSection.vue'

// 获取路由参数
const route = useRoute()
const heritageId = route.params.id

// 加载状态
const isLoading = ref(true)
const error = ref(null)

// 非遗文化数据
const heritageData = reactive({
  name: '',
  imageGroups: [],
  introduction: '',
  details: [],
  features: [],
  coverImage: '',
  logo: '',
  category: '',
  createTime: null,
  updateTime: null
})

// 获取非遗详情
const fetchHeritageDetail = async () => {
  try {
    isLoading.value = true
    error.value = null

    // 获取基本信息
    const detailResponse = await getHeritageDetail(heritageId)
    if (detailResponse && detailResponse.result) {
      const data = detailResponse.result
      heritageData.name = data.name || '未知名称'
      heritageData.introduction = data.description || '暂无描述'
      heritageData.coverImage = data.coverImage || ''
      heritageData.logo = data.logo || ''
      heritageData.category = data.category || ''
      heritageData.createTime = data.createTime
      heritageData.updateTime = data.updateTime

      // 设置详情信息
      heritageData.details = [
        { label: '分类', value: data.category === 'local' ? '本地非遗' : '在线非遗' },
        { label: '创建时间', value: data.createTime ? new Date(data.createTime * 1000).toLocaleDateString() : '未知' },
        { label: '更新时间', value: data.updateTime ? new Date(data.updateTime * 1000).toLocaleDateString() : '未知' }
      ]
    }

    // 获取图片列表
    try {
      const imageResponse = await getHeritageImageList(heritageId)
      if (imageResponse && imageResponse.result && Array.isArray(imageResponse.result)) {
        // 将图片数据转换为组件需要的格式
        const images = imageResponse.result.map((img, index) => ({
          url: img.imageUrl || img.url || heritageData.coverImage,
          title: img.title || img.name || `图片 ${index + 1}`
        }))

        // 将图片分组，每组4张
        const groupSize = 4
        const groups = []
        for (let i = 0; i < images.length; i += groupSize) {
          groups.push(images.slice(i, i + groupSize))
        }
        heritageData.imageGroups = groups.length > 0 ? groups : [[{
          url: heritageData.coverImage || '/src/assets/courseCover/cover1.png',
          title: '封面图片'
        }]]
      } else {
        // 如果没有图片数据，使用封面图片
        heritageData.imageGroups = [[{
          url: heritageData.coverImage || '/src/assets/courseCover/cover1.png',
          title: '封面图片'
        }]]
      }
    } catch (imgError) {
      console.warn('获取图片列表失败，使用默认图片:', imgError)
      heritageData.imageGroups = [[{
        url: heritageData.coverImage || '/src/assets/courseCover/cover1.png',
        title: '封面图片'
      }]]
    }

  } catch (err) {
    console.error('获取非遗详情失败:', err)
    error.value = err.response?.data?.msg || err.message || '获取数据失败'
    ElMessage.error(error.value)

    // 使用模拟数据作为后备
    heritageData.name = '数据加载失败'
    heritageData.introduction = '无法获取详细信息，请稍后重试。'
    heritageData.imageGroups = [[{
      url: '/src/assets/courseCover/cover1.png',
      title: '默认图片'
    }]]
    heritageData.details = [
      { label: '状态', value: '数据加载失败' }
    ]
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  if (heritageId) {
    fetchHeritageDetail()
  } else {
    ElMessage.error('缺少非遗ID参数')
    error.value = '缺少必要参数'
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.heritage-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #8a6de3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  color: #dc3545;
  margin-bottom: 16px;
  text-align: center;
  font-size: 16px;
}

.retry-btn {
  padding: 10px 20px;
  background: #8a6de3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.retry-btn:hover {
  background: #7a5dd3;
}
</style>